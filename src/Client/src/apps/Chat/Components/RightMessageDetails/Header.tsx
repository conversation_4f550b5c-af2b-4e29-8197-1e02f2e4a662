import { RootState } from "@/store/Reducers";
import { TagsOutlined, UserAddOutlined } from "@ant-design/icons";
import { Col, Drawer, Row, Tooltip, Typography } from "antd";
import { useTranslation } from "react-i18next";
import { useSelector } from "react-redux";
import { useGetChatMessageDetails } from "../../ServerSideStates";
import { useState } from "react";
import AddOrUpdateCustomerIndex from "@/apps/Admin/Pages/Customers/Components/AddOrUpdate/AddOrUpdateCustomerIndex";
import { useSearchParams } from "react-router-dom";
import AddOrUpdateIndex from "@/apps/Admin/Pages/Ticket/Components/AddOrUpdateIndex";

const Header = () => {

  const { selectedChatItem } = useSelector((state: RootState) => state.chat);
  const chatDetails = useGetChatMessageDetails(selectedChatItem?.Id);
  const { t } = useTranslation();
  const { Text } = Typography;
  const [searchParams, setSearchParams] = useSearchParams()
  const [isShowCustomerDrawer, setIsShowCustomerDrawer] = useState(false)
  const [isShowTicketDrawer, setIsShowTicketDrawer] = useState(false)
  const [mode,setMode] = useState<"customer"|"ticket"|null>(null)
  let name = chatDetails?.data?.Value?.CustomerName || ""

  const initials = name
    .split(" ")
    .map((n: any) => n[0])
    .join("")
    .toUpperCase();


  return (
    <Row>
      <Col xs={24}>
        <Row>
          <Col xs={12}>
            <div className="!flex items-center gap-2">
              <div className="!w-[60px] !h-[50px] !bg-[#58666e] !flex items-center justify-center !cursor-pointer !relative">
                <Text className="!text-lg !text-white">{initials}</Text>

              </div>
              <div className="!flex flex-col gap-1 " >
                <Text>{name}</Text>
                <Text className="!text-xs !text-gray-600" >{chatDetails?.data?.Value?.ExternalId}</Text>
              </div>
            </div>
          </Col>
          <Col xs={12} className="!flex justify-end items-center gap-2 px-4">
            <Tooltip title={chatDetails?.data?.Value?.CustomerId?t("chat.createTicket"):t("chat.createCustomerAndCreateTicket")}>
              <TagsOutlined className="!text-[#b5b5b5] !text-2xl cursor-pointer"
                onClick={async() => {
                  await setMode("ticket")
                  if(chatDetails?.data?.Value?.CustomerId){
                    setSearchParams({ customerId: chatDetails?.data?.Value?.CustomerId,ticketChatId:chatDetails?.data?.Value?.Id })
                    setIsShowTicketDrawer(true)
                  } 
                  else{
                    setSearchParams({ chatCustomerName: name })
                    setSearchParams({ ticketChatId: chatDetails?.data?.Value?.CustomerId, })
                   
                    setIsShowCustomerDrawer(true)
                  }
                  
                }}
              />
            </Tooltip>
            {
              !chatDetails?.data?.Value?.CustomerId && (
                <Tooltip title={t("chat.createAsCustomer")}>
                  <UserAddOutlined onClick={async () => {
                    await setMode("customer")
                    setSearchParams({ chatCustomerName: name?.replace("~",""),chatCustomerPhone:chatDetails?.data?.Value?.ExternalId })
                    setIsShowCustomerDrawer(true)
                  }} className="!text-[#b5b5b5] !text-2xl cursor-pointer" />
                </Tooltip>
              )
            }



          </Col>
        </Row>
      </Col>
      <Drawer
        open={isShowCustomerDrawer}
        onClose={() => {
          

          if(mode==="ticket"){
            
            if(searchParams.get("customerId")){
              setIsShowCustomerDrawer(false)
              setIsShowTicketDrawer(true)
            }
            else{
              setIsShowCustomerDrawer(false) 
              setIsShowTicketDrawer(false)
              setSearchParams({})
            }
          }
          else{
            setIsShowCustomerDrawer(false)
            setSearchParams({})
          }

          setMode(null)


        }}
        width={"80%"}

      >
        < AddOrUpdateCustomerIndex type="drawer" />
      </Drawer>

      <Drawer
        open={isShowTicketDrawer}
        onClose={() => {
          setIsShowTicketDrawer(false)
         
          setSearchParams({})

        }}
        title={t("ticket.list.addTicket")}
        width={"80%"}

      >
        <AddOrUpdateIndex />
      </Drawer>
    </Row>
  );
};

export default Header;
