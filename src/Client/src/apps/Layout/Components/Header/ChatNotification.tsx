

import { handleSetChatHubConnection } from "@/apps/Chat/ClientSideStates";
import { openNotificationWithIcon } from "@/helpers/OpenNotificationWithIcon";
import { setBackEndUrl } from "@/helpers/SetBackEndUrl";
import { commonRoutePrefix } from "@/routes/Prefix";
import { RootState } from "@/store/Reducers";
import { ArrowRightOutlined } from "@ant-design/icons";
import {
  HubConnectionBuilder,
  LogLevel,
  HttpTransportType,
} from "@microsoft/signalr";
import { useEffect,  } from "react";
import { useTranslation } from "react-i18next";
import { useQueryClient } from "react-query";
import { useDispatch, useSelector } from "react-redux";
import {  useLocation, useNavigate } from "react-router-dom";
import endPoints from "@/apps/Chat/EndPoints"


const ChatNotification = () => {
  const queryClient = useQueryClient()
  const { t } = useTranslation();
  const navigate = useNavigate()
    const dispatch = useDispatch()
    const location = useLocation()


  
    
    const { hubConnection:connection } = useSelector(
        (state: RootState) => state.chat
      );
    
     useEffect(() => {
         const newConnection = new HubConnectionBuilder()
           .withUrl(`${setBackEndUrl()}/hubs/chathub`, {
             accessTokenFactory: () => localStorage.getItem("access_token") || "",
             skipNegotiation: true,
             transport: HttpTransportType.WebSockets,
             withCredentials: true,
           })
           .configureLogging(LogLevel.Information)
           .withAutomaticReconnect()
           .build();
     
         newConnection
           .start()
           .then(() =>  dispatch(handleSetChatHubConnection({ connection:newConnection })))
           .catch((err) => console.error("SignalR bağlantı hatası:", err));
     
         return () => {
           newConnection.stop();
         };
       }, []);

       useEffect(() => {
        if (connection) {
          connection.on("NewMessage", () => {
            queryClient.resetQueries({
              queryKey: endPoints.getUserChatListFilter,
              exact: false,
            });
            queryClient.resetQueries({
              queryKey: endPoints.getChatMessageDetails,
              exact: false,
            });
            {
              if(!location.pathname.includes("/chat")){
            openNotificationWithIcon("info",t("chat.youHaveNewMessage"),
            <span
            className="!text-white !cursor-pointer !flex justify-between items-center" 
        
            onClick={()=>{
             
              
              navigate(`${commonRoutePrefix}/chat`)
            }}
            >
              <span >
              {t("chat.clickToRead")} 
              </span>
            
            <span className="!w-[25px] !h-[25px] !border !border-white !rounded-full !flex items-center justify-center">
            <ArrowRightOutlined />
            </span>
            </span>)
             
              }
            }
          });
        }
    
        return () => {
          if (connection) {
            connection.off("NewMessage");
          }
        };
      }, [connection]);


       
    return ( <></> );
}
 
export default ChatNotification;