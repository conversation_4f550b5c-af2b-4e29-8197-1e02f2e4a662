import { RootState } from "@/store/Reducers";
import { Tabs, TabsProps } from "antd";
import { useDispatch, useSelector } from "react-redux";
import { hanldleSetSelectedUserListChatItem, hanldleSetUserChatFilter } from "../../ClientSideStates";
import { useTranslation } from "react-i18next";

const TabStatus = () => {
  const {t} = useTranslation()
    const {userChatFilter:filter} = useSelector((state:RootState)=>state.chat)
    const dispatch = useDispatch()
    const onChange = (key: string) => {
        const currentFilter = {...filter,status:Number(key)}
       dispatch(hanldleSetUserChatFilter({ filter:currentFilter }));
       dispatch(hanldleSetSelectedUserListChatItem({ data: null }))
       
    }
      const items: TabsProps['items'] = [
        {
          key: "1",
          label: t("chat.open"),
          
        },
        {
          key: "2",
          label: t("chat.closed"),
         
        },
        {
          key: "3",
          label: t("chat.archived"),
          
        },
      ];

    return ( <>
    <Tabs activeKey={filter?.status?.toString()} defaultActiveKey="1" items={items} onChange={onChange} />
    
    </> );
}
 
export default TabStatus;