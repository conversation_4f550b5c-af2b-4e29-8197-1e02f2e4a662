import { DataResponse } from "@/services/BaseResponseModel";
import endpoints from "./EndPoints";
import headers from "@/services/Headers.json";
import { get, post, put } from "@/services/Client";
import { CreateUrlFilter } from "@/helpers/CreateURLFilter";


export const getUserChatListFilter = async (
  inputFilter: any
): Promise<DataResponse<any>> => {
  const query = inputFilter ? CreateUrlFilter(inputFilter) : null;

  const url = `${endpoints.getUserChatListFilter}?${query || ""}`;

  const config = headers.content_type.application_json;
  return get<DataResponse<any>>(url, config);
};

export const getChatMessageDetails = async (
  chatId:string
): Promise<DataResponse<any>> => {
  const url = `${endpoints.sendMessage}/${chatId}`;
  const config = headers.content_type.application_json;
  return get<DataResponse<any>>(url, config);
};

export const sendMessage = async (data: any): Promise<DataResponse<any>> => {
  const url = `${endpoints.sendMessage}/${data.ChatId}/messages`;
  const config = headers.content_type.application_json;
  return post<DataResponse<any>>(url, data, config);
};

export const createNewChat = async (data: any): Promise<DataResponse<any>> => {
  const url = `${endpoints.createNewChat}`;
  const config = headers.content_type.application_json;
  return post<DataResponse<any>>(url, data, config);
};

export const replayMessage = async (data: any): Promise<DataResponse<any>> => {
  const url = `${endpoints.replayMessage}/${data.ChatId}/reply`;
  const config = headers.content_type.application_json;
  return post<DataResponse<any>>(url, data, config);
};



export const changeChatStatusToArchived = async (
  data: any
): Promise<DataResponse<any>> => {
  const url = `${endpoints.changeChatStatusToArchved}/${data.Id}/archive`;
  const config = headers.content_type.application_json;
  return put<DataResponse<any>>(url, data, config);
};

export const changeChatStatusToClosed = async (
  data: any
): Promise<DataResponse<any>> => {
  const url = `${endpoints.changeChatStatusToClosed}/${data.Id}/close`;
  const config = headers.content_type.application_json;
  return put<DataResponse<any>>(url, data, config);
};

