import { <PERSON>, <PERSON><PERSON><PERSON>,  <PERSON><PERSON>,  <PERSON>, Toolt<PERSON> } from "antd";
import Search from "./Search";
import { useState } from "react";
import { PlusOutlined } from "@ant-design/icons";
import StartChat from "./StartChat";
import { useTranslation } from "react-i18next";


const TopOptions = () => {
  const {t} = useTranslation()
  const [isShowNewConversation, setIsShowNewConversation] = useState(false);
  // const [isShowCreateGroupChat, setIsShowCreateGroupChat] = useState(false);
  // const { t } = useTranslation();
  // const items: MenuProps["items"] = [
  //   {
  //     key: "1",
  //     label: t("chat.startChat"),
  //     onClick: () => {
  //       setIsShowNewConversation(true);
  //     },
  //   },
  //   {
  //     key: "2",
  //     label: t("chat.createGroupChat"),
  //     onClick: () => {
  //       setIsShowCreateGroupChat(true);
  //     },
  //   },
  // ];

  return (
    <>
      <Row className="menu-height">
        <Col xs={24} xl={6} className="" >
          {/* <div className="!flex items-center justify-center !w-[30px] !h-full">
            <Dropdown
              menu={{ items }}
              trigger={["click"]}
              placement="bottomLeft"
            >
              <div className="flex items-center justify-center w-[30px] h-full cursor-pointer">
                <PlusOutlined className="text-base text-gray-300" />
              </div>
            </Dropdown>
          </div> */}
          <div className="!flex !w-full"  > 

          <Tooltip title={t("chat.newConversation")} >
            <PlusOutlined className="text-base text-gray-500 cursor-pointer"
            onClick={()=>{
              setIsShowNewConversation(true)
            }}
            />
          </Tooltip>
          <Search />
          </div>
        </Col>
        <Col xs={24}>
          <Divider className="!m-0 !text-gray-400" />
        </Col>
      </Row>

      <Modal
        open={isShowNewConversation}
        onCancel={() => {
          setIsShowNewConversation(false);
        }}
        title={t("chat.createChatTile")}
        footer={false}
      >
        <StartChat onFinish={()=>{
          setIsShowNewConversation(false);
        }}  />
      </Modal>

      {/* <Modal
        open={isShowCreateGroupChat}
        onCancel={() => {
          setIsShowCreateGroupChat(false);
        }}
        footer={false}
      >
        <CreateGroupChat />
      </Modal> */}
    </>
  );
};

export default TopOptions;
