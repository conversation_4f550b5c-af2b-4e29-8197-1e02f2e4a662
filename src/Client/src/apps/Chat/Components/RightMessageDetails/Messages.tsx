import { RootState } from "@/store/Reducers";
import dayjs from "dayjs";
import { useSelector } from "react-redux";
import { useGetChatMessageDetails } from "../../ServerSideStates";
import { useEffect, useRef, } from "react";

import { useQueryClient } from "react-query";
import endPoints from "../../EndPoints";
import { Avatar, Divider, Typography } from "antd";
import MessageItemOptions from "./MessageItemOptions";

const Messages = () => {
  const { hubConnection:connection } = useSelector(
    (state: RootState) => state.chat
  );
  const { selectedChatItem,  } = useSelector(
    (state: RootState) => state.chat
  );
  const chatDetails = useGetChatMessageDetails(selectedChatItem?.Id);
  const messages = chatDetails?.data?.Value?.Messages || [];
  const messagesLength = messages.length;
  const { Text } = Typography;
  const incomingName = chatDetails?.data?.Value?.CustomerName || "";
  const initialsIncoming = incomingName
    .split(" ")
    .map((n: any) => n[0])
    .join("")
    .toUpperCase();

  
    

    console.log("chatDetails?.data?.Value?.SenderUserName",chatDetails?.data?.Value?.SenderUserName)


  const queryClient = useQueryClient();
  const lastMessageRef = useRef<HTMLDivElement | null>(null);




  // Scroll mesaj konteyneri içinde en alta
  useEffect(() => {
    if (lastMessageRef.current) {
      lastMessageRef.current.scrollIntoView({
        behavior: "smooth",
        block: "end",
      });
    }
  }, [messagesLength]);

  const scrollToMessage = (messageId: string) => {
    const element = document.getElementById(`message-${messageId}`);
    if (element) {
      element.scrollIntoView({ behavior: "smooth", block: "center" });
  
      // (Opsiyonel) Geçici vurgulama
      element.classList.add("bg-yellow-100");
      setTimeout(() => element.classList.remove("bg-yellow-100"), 1500);
    }
  };
  
  

  return (
    <div
      className="h-[calc(100vh-200px)] overflow-y-auto px-4"
      id="message-container"
    >
      <div className="space-y-4 py-4">
        {messages.map((msg: any, index: number) => {
          const isLast = index === messagesLength - 1;

          return (
            <div
              key={index}
              id={`message-${msg.Id}`}
              ref={isLast ? lastMessageRef : null}
              className={`flex items-start space-x-1 ${
                msg.Direction !== "Incoming" ? "justify-end" : "justify-start"
              }`}
            >
              {msg.Direction === "Incoming" && (
                <Avatar className="!bg-[#58666e] !text-white">
                  {initialsIncoming}
                </Avatar>
              )}

              <div
                className={`p-4 rounded-lg max-w-xs flex flex-col gap-1 !relative ${
                  msg.Direction !== "Incoming"
                    ? "bg-gray-200 text-gray-900"
                    : "bg-yellow-100 text-gray-900"
                }`}
              >
                {
                   msg.Direction === "Incoming"&&
                <div className={`!flex justify-end !w-full !absolute right-0`}>
                  <MessageItemOptions item={msg} />
                </div>
                }
               
                 
           
                {msg.ReplyToMessageId && msg.ReplyToContent && (
                  <div
                    onClick={() => scrollToMessage(msg?.ReplyToMessageId )}
                    className="bg-[#58666e] !p-2 !rounded-md opacity-55 cursor-pointer hover:opacity-80"
                  >
                    <Text className="!text-white">
                      {msg.ReplyToContent.length > 10
                        ? `${msg.ReplyToContent.substring(0, 10)}...`
                        : msg.ReplyToContent}
                    </Text>
                  </div>
                )}

                <p className="text-xs">{msg.Content}</p>
                <p className="text-xs text-gray-500 mt-1">
                  {dayjs(msg.SentAt).format("DD.MM.YYYY HH:mm")}
                </p>
              </div>

              {msg.Direction !== "Incoming" && (
                <Avatar className="!bg-gray-200 !text-black">
                  {

                    msg.SenderUserName?.split(" ")
                    .map((n: any) => n[0])
                    .join("")
                    .toUpperCase()
                  }
                </Avatar>
              )}
            </div>
          );
        })}
      </div>
    </div>
  );
};

export default Messages;
