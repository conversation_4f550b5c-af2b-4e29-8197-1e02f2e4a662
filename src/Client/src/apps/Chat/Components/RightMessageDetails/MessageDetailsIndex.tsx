import { useSelector } from "react-redux";
import Header from "./Header";
import Messages from "./Messages";

import { RootState } from "@/store/Reducers";
import ReplyMessage from "./ReplyMessage";
import SendMessage from "./SendMessage";

const MessageDetailsIndex = () => {
  const { selectedMessageItem,userChatFilter } = useSelector((state: RootState) => state.chat);
  return (
    <div className="flex flex-col h-full">
      <div className="px-4 py-2 border-b border-gray-300">
        <Header />
      </div>

      <div className={`flex-1 overflow-auto  space-y-2 ${selectedMessageItem ? "!pb-[180px]" : "!pb-[120px]"
        }`}>
        <Messages />
      </div>
        {
          userChatFilter?.status===1&&
          <>
      <div className="border-t border-gray-300 p-4 bg-white !fixed bottom-0 !w-full ">

      {selectedMessageItem && <ReplyMessage />}
      <SendMessage />
      </div>
          </>
        }
    </div>
  );
};

export default MessageDetailsIndex;
