using MediatR;
using Microsoft.EntityFrameworkCore;
using Requests.Application.Abstractions;
using Shared.Application;
using Shared.Contracts;

namespace Requests.Application.Reports.DashboardTicketReports;

public class DashboardTicketReportQueryHandler(
    IRequestsDbContext context
) : IRequestHandler<DashboardTicketReportQuery, Result<DashboardTicketReportDto>>
{
    private readonly IRequestsDbContext _context = context;

    public async Task<Result<DashboardTicketReportDto>> Handle(DashboardTicketReportQuery request, CancellationToken cancellationToken)
    {
        var startDate = request.StartDate ?? DateTime.MinValue;
        var endDate = request.EndDate ?? DateTime.MaxValue;

        var query = _context.Tickets
            .Where(t => t.InsertDate >= startDate && t.InsertDate <= endDate);

        var totalTicketCount = await query.CountAsync(cancellationToken);

        var assignedTicketCount = await query
            .Where(t => t.UserId != null && t.UserId != Guid.Empty)
            .CountAsync(cancellationToken);

        var unassignedTicketCount = await query
            .Where(t => t.UserId == null || t.UserId == Guid.Empty)
            .CountAsync(cancellationToken);

        var closedTicketCount = await query
            .Where(t => t.Status != null && t.Status.NodeType == Domain.NodeType.End)
            .CountAsync(cancellationToken);

        var openTicketCount = await query
            .Where(t => t.Status != null && t.Status.NodeType != Domain.NodeType.End)
            .CountAsync(cancellationToken);

        var dto = new DashboardTicketReportDto
        {
            TotalTicketCount = totalTicketCount,
            OpenTicketCount = openTicketCount,
            AssignedTicketCount = assignedTicketCount,
            UnassignedTicketCount = unassignedTicketCount,
            ClosedTicketCount = closedTicketCount
        };

        return Result.Success(dto);
    }
}
