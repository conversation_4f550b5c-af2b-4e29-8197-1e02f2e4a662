import { MazakaButton } from "@/apps/Common/MazakaButton";
import { MazakaTextArea } from "@/apps/Common/MazakaTextarea";
import { showErrorCatching } from "@/helpers/ShowErrorCatching";
import { SendOutlined } from "@ant-design/icons";
import { Form } from "antd";
import { useTranslation } from "react-i18next";
import { MazakaForm } from "@/apps/Common/MazakaForm";
import { useDispatch, useSelector } from "react-redux";
import { RootState } from "@/store/Reducers";
import { replayMessage, sendMessage } from "../../Services";

import { useRef } from "react";
import { handleSetSelectedChatMessageItem } from "../../ClientSideStates";

const SendMessage = () => {
  const dispatch = useDispatch();
  const [form] = Form.useForm();
  const {
    selectedChatItem,
    selectedMessageItem,
    hubConnection: connection,
  } = useSelector((state: RootState) => state.chat);
  const { userInfoes } = useSelector((state: RootState) => state.profile);
  const { t } = useTranslation();
  const typingStartedRef = useRef(false); // Şu anda typingStarted mı?

  const chatId = selectedChatItem?.Id;

  const handleOnFinish = async () => {
    const formValues = form.getFieldsValue();
    if (!formValues["Content"]) {
      return false;
    }
    formValues["ChatId"] = chatId;
    formValues["Direction"] = 2;
    formValues["SenderId"] = userInfoes?.Id;
    formValues["ContentType"] = 1;
    if (selectedMessageItem) {
      formValues["ReplyToMessageId"] = selectedMessageItem.Id;
    }

    try {
      if (typingStartedRef.current && connection && chatId) {
        await connection.invoke("TypingStopped", chatId);
        typingStartedRef.current = false;
      }

      selectedMessageItem
        ? await replayMessage(formValues)
        : await sendMessage(formValues);
     
      form.resetFields();
      if (selectedMessageItem) {
        dispatch(handleSetSelectedChatMessageItem({ data: null }));
      }
    } catch (error) {
      showErrorCatching(error, null, false, t);
    }
  };

  const handleFocus = async () => {
    const content = form.getFieldValue("Content");
    if (connection && chatId && content?.trim()) {
      await connection.invoke("TypingStarted", chatId);
      typingStartedRef.current = true;
    }
  };

  const handleBlur = async () => {
    if (connection && chatId && typingStartedRef.current) {
      await connection.invoke("TypingStopped", chatId);
      typingStartedRef.current = false;
    }
  };

  const handleChange = async (e: any) => {
    const value = e.target.value;
    const isEmpty = !value?.trim();

    if (connection && chatId) {
      if (!isEmpty && !typingStartedRef.current) {
        await connection.invoke("TypingStarted", chatId);
        typingStartedRef.current = true;
      }

      if (isEmpty && typingStartedRef.current) {
        await connection.invoke("TypingStopped", chatId);
        typingStartedRef.current = false;
      }
    }
  };

  return (
    <MazakaForm
      form={form}
      onFinish={handleOnFinish}
      submitButtonVisible={false}
    >
      <MazakaTextArea
        autoSize
        label=""
        name="Content"
        placeholder={t("chat.writeYourMessage")}
        className="!mb-2"
        bordered={false}
        onFocus={handleFocus}
        onBlur={handleBlur}
        onChange={handleChange}
      />

      <MazakaButton htmlType="submit" icon={<SendOutlined />} status="save">
        {t("chat.send")}
      </MazakaButton>
    </MazakaForm>
  );
};

export default SendMessage;
