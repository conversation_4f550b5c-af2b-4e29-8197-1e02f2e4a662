using MediatR;
using Microsoft.AspNetCore.Builder;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Routing;
using Shared.Endpoints;

namespace Requests.Application.Reports.DashboardTicketReports;

public class DashboardTicketReportEndpoint : IEndpoint
{
    public void MapEndpoint(IEndpointRouteBuilder app)
    {
        app.MapGet("/api/v1/requests/tickets/dashboard", async (
            [FromQuery] DateTime? startDate,
            [FromQuery] DateTime? endDate,
            ISender sender,
            CancellationToken cancellationToken) =>
        {
            var result = await sender.Send(new DashboardTicketReportQuery
            {
                StartDate = startDate,
                EndDate = endDate
            }, cancellationToken);

            return result.Match(Results.Ok, CustomResults.Problem);
        })
        .WithTags("Requests.Reports")
        .WithGroupName("apiv1")
        .RequireAuthorization("Requests.Reports")
        .Produces<DashboardTicketReportDto>(StatusCodes.Status200OK)
        .ProducesProblem(StatusCodes.Status400BadRequest);
    }
}
