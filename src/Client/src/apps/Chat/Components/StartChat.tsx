import GeneralCustomer from "@/apps/Common/GeneralCustomer";
import { MazakaButton } from "@/apps/Common/MazakaButton";
import { MazakaForm } from "@/apps/Common/MazakaForm";
import { openNotificationWithIcon } from "@/helpers/OpenNotificationWithIcon";
import { showErrorCatching } from "@/helpers/ShowErrorCatching";
import useMazakaForm from "@/hooks/useMazakaForm";
import { Col, Form, Row } from "antd";
import { FC } from "react";
import { useTranslation } from "react-i18next";
import { createNewChat } from "../Services";
import { useSelector } from "react-redux";
import { RootState } from "@/store/Reducers";

const StartChat: FC<{ onFinish: () => void }> = ({ onFinish }) => {
  const { userInfoes } = useSelector((state: RootState) => state.profile);
  const [form] = Form.useForm();
  const { formActions, mazakaForm } = useMazakaForm(form);
  const { t } = useTranslation();

  const handleOnFinish = async () => {
    mazakaForm.setLoading();
    const formValues = form.getFieldsValue();
    formValues["AssignedUserId"] = userInfoes?.Id;
    formValues["Channel"] = 1 //whatsap

    try {
      await createNewChat(formValues);
      mazakaForm.setSuccess(2000, () => t("form.transactionSuccessful"));
      openNotificationWithIcon("success", t("form.transactionSuccessful"));
      form.resetFields();

      // queryClien.resetQueries({
      //   queryKey: endPoints.getWorkFlowListFilter,
      //   exact: false,
      // });
      onFinish();
    } catch (error) {
      showErrorCatching(error, mazakaForm, true, t);
    }
  };
  return (
    <>
      <MazakaForm form={form} onFinish={handleOnFinish} submitButtonVisible={false}>
        <Row gutter={[20, 10]}>
          <GeneralCustomer
          name="CustomerId"
            label={t("chat.customer")}
            placeholder={t("chat.customer")}
            xs={24}
            rules={[{ required: true, message: "" }]}
            onChange={(value: string, obj: any) => {
              if(!obj?.phonePrefix||!obj?.phone){
               openNotificationWithIcon("error","secil kullanicida telefon kayitli degil")
              }
              else{

                form.setFieldValue("CustomerId", value);
                form.setFieldValue("ExternalId", obj?.phonePrefix+ obj?.phone);
              }
             
            }}
          />
          <Col xs={24}>
            <MazakaButton
              processType={formActions.submitProcessType}
              htmlType="submit"
              status="save"
            >
              {t("chat.create")}
            </MazakaButton>
          </Col>

          <Form.Item name={"ExternalId"} className="!hidden"></Form.Item>
        </Row>
      </MazakaForm>
    </>
  );
};

export default StartChat;
